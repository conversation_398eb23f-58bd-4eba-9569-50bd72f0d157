import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react';
import { Button, Toast, Modal } from 'antd-mobile';
import { RiWechatPayFill } from 'react-icons/ri';
import { wechatJSAPIPayService } from '@/services/wechat-pay';
import { wechatRechargePayService } from '@/services/wechat-pay/recharge';
import { wechatOrderPayService } from '@/services/wechat-pay/order';
import { useWechatAuth } from '@/components/wechat-auth';
import { to } from '@/utils';
import styles from './styles.module.scss';
import { isH5Program } from '@/utils/platform';

interface WechatJSAPIPayProps {
  /** 订单信息 */
  orderInfo: {
    purse_detail_id?: string; // 充值订单ID
    order_id?: string; // 商品订单ID
    trade_no?: string; // 交易号（根据接口文档）
    total_fee?: string; // 支付金额（元）
    out_trade_no?: string; // 商户订单号（兼容旧版本）
    description?: string; // 商品描述
    amount?: number; // 金额，单位：分（兼容旧版本）
    attach?: string;
  };
  /** 支付类型 */
  paymentType?: 'recharge' | 'general';
  /** 支付成功回调 */
  onPaySuccess?: (result: any) => void;
  /** 支付失败回调 */
  onPayError?: (error: Error) => void;
  /** 支付取消回调 */
  onPayCancel?: () => void;
  /** 自定义按钮文本 */
  buttonText?: string;
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default';
  /** 是否显示为块级按钮 */
  block?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 微信JSAPI支付组件
 * 集成微信公众号授权和支付流程
 */
const WechatJSAPIPay: React.FC<WechatJSAPIPayProps> = ({
  orderInfo,
  paymentType = 'general',
  onPaySuccess,
  onPayError,
  onPayCancel,
  buttonText = '微信支付',
  disabled = false,
  buttonType = 'primary',
  block = true,
  className,
}) => {
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { openid, isAuthorizing, startAuth, isAuthorized } = useWechatAuth();
  const isMountedRef = useRef(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  /**
   * 处理支付按钮点击
   */
  const handlePayClick = async () => {
    try {
      // 检查是否在微信浏览器中
      if (!isH5Program()) {
        Toast.show({
          icon: 'fail',
          content: '请在微信中打开',
        });
        return;
      }

      // 检查是否已授权
      if (!isAuthorized) {
        Toast.show({
          icon: 'loading',
          content: '正在获取授权...',
          duration: 0,
        });
        
        try {
          await startAuth();
          Toast.clear();
        } catch (error) {
          Toast.clear();
          Toast.show({
            icon: 'fail',
            content: '授权失败，请重试',
          });
          onPayError?.(error as Error);
          return;
        }
      }

      // 开始支付流程
      await startPayment();
    } catch (error) {
      console.error('支付流程错误:', error);
      onPayError?.(error as Error);
    }
  };

  /**
   * 开始支付流程
   */
  const startPayment = async () => {
    setIsPaymentLoading(true);
    setShowPaymentModal(true);

    try {
      // 获取有效的openid
      let currentOpenid = openid;
      if (!currentOpenid) {
        // 尝试从存储中获取有效的授权数据
        try {
          const authDataStr = localStorage.getItem('wechat_auth_data');
          if (authDataStr) {
            const authData = JSON.parse(authDataStr);
            if (authData.expires_at && Date.now() < authData.expires_at) {
              currentOpenid = authData.openid;
            }
          }
        } catch (error) {
          console.error('解析授权数据失败:', error);
        }
      }
      
      if (!currentOpenid) {
        throw new Error('用户授权已过期，请重新授权');
      }

      // 根据支付类型选择不同的支付流程
      if (paymentType === 'recharge') {
        await handleRechargePay(currentOpenid);
      } else {
        await handleGeneralPay(currentOpenid);
      }
    } catch (error) {
      console.error('支付失败:', error);
      Toast.show({
        icon: 'fail',
        content: error instanceof Error ? error.message : '支付失败',
      });
      onPayError?.(error as Error);
    } finally {
      setIsPaymentLoading(false);
      setShowPaymentModal(false);
    }
  };

  /**
   * 处理充值支付
   */
  const handleRechargePay = async (currentOpenid: string) => {
    if (!orderInfo.purse_detail_id || !orderInfo.total_fee) {
      throw new Error('充值订单信息不完整');
    }

    Toast.show({
      icon: 'loading',
      content: '正在创建充值订单...',
      duration: 0,
    });

    const rechargeData = {
      purse_detail_id: orderInfo.purse_detail_id,
      total_fee: orderInfo.total_fee,
      openid: currentOpenid,
    };

    const result = await wechatRechargePayService.executeRechargePay(rechargeData);
    
    Toast.clear();

    if (result.success) {
      Toast.show({
        icon: 'success',
        content: '充值成功',
      });
      onPaySuccess?.(result);
    } else if (result.cancelled) {
      Toast.show({
        icon: 'fail',
        content: '充值已取消',
      });
      onPayCancel?.();
    } else {
      throw new Error(result.message || '充值失败');
    }
  };

  /**
   * 处理通用支付（订单支付）
   */
  const handleGeneralPay = async (currentOpenid: string) => {
    if (!orderInfo.order_id || !orderInfo.total_fee) {
      throw new Error('订单支付信息不完整');
    }

    Toast.show({
      icon: 'loading',
      content: '正在创建订单支付...',
      duration: 0,
    });

    const orderData = {
      trade_no: orderInfo.order_id || orderInfo.trade_no || '', // 兼容不同的订单ID字段
      total_fee: orderInfo.total_fee,
      openid: currentOpenid,
    };

    const result = await wechatOrderPayService.executeOrderPay(orderData);

    Toast.clear();

    if (result.success) {
      Toast.show({
        icon: 'success',
        content: '支付成功',
      });
      onPaySuccess?.(result);
    } else if (result.cancelled) {
      Toast.show({
        icon: 'fail',
        content: '支付已取消',
      });
      onPayCancel?.();
    } else {
      throw new Error(result.message || '支付失败');
    }
  };

  /**
   * 验证支付结果（带重试机制）
   */
  const verifyPaymentResult = async (retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = 2000; // 2秒

    try {
      Toast.show({
        icon: 'loading',
        content: `正在确认支付结果${retryCount > 0 ? `(${retryCount}/${maxRetries})` : ''}...`,
        duration: 0,
      });

      let orderStatus: any;

      // 根据支付类型选择不同的查询方法
      if (paymentType === 'recharge') {
        const purseDetailId = orderInfo.purse_detail_id || '';
        const [err, res] = await to(
          wechatJSAPIPayService.queryRechargeStatus(purseDetailId)
        );
        if (err) throw new Error('查询充值状态失败');
        orderStatus = res;
      } else {
        const orderId = orderInfo.order_id || orderInfo.out_trade_no || '';
        const [err, res] = await to(
          wechatJSAPIPayService.queryOrderPaymentStatus(orderId)
        );
        if (err) throw new Error('查询订单状态失败');
        orderStatus = res;
      }

      Toast.clear();

      if (orderStatus?.trade_state === 'SUCCESS') {
        Toast.show({
          icon: 'success',
          content: '支付成功',
        });
        onPaySuccess?.(orderStatus);
      } else if (orderStatus?.trade_state === 'USERPAYING' && retryCount < maxRetries) {
        // 用户支付中，等待后重试
        timeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            verifyPaymentResult(retryCount + 1);
          }
        }, retryDelay);
      } else if (orderStatus?.trade_state === 'NOTPAY' && retryCount < maxRetries) {
        // 未支付，等待后重试
        timeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            verifyPaymentResult(retryCount + 1);
          }
        }, retryDelay);
      } else {
        throw new Error(orderStatus?.trade_state_desc || '支付状态异常');
      }
    } catch (error) {
      console.error('验证支付结果失败:', error);

      if (retryCount < maxRetries) {
        // 重试
        timeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            verifyPaymentResult(retryCount + 1);
          }
        }, retryDelay);
      } else {
        // 重试次数用完，显示错误
        Toast.show({
          icon: 'fail',
          content: '支付结果确认失败，请稍后查看订单状态',
        });
        onPayError?.(error as Error);
      }
    }
  };

  /**
   * 关闭支付弹窗
   */
  const handleCloseModal = () => {
    if (!isPaymentLoading) {
      setShowPaymentModal(false);
    }
  };

  return (
    <>
      <Button
        color={buttonType}
        block={block}
        disabled={disabled || isAuthorizing || isPaymentLoading}
        onClick={handlePayClick}
        className={`${styles.wechatPayButton} ${className || ''}`}
        loading={isAuthorizing || isPaymentLoading}
      >
        <RiWechatPayFill className={styles.wechatIcon} />
        {isAuthorizing ? '正在授权...' : isPaymentLoading ? '支付中...' : buttonText}
      </Button>

      {/* 支付进度弹窗 */}
      <Modal
        visible={showPaymentModal}
        content={
          <div className={styles.paymentModal}>
            <div className={styles.paymentIcon}>
              <RiWechatPayFill />
            </div>
            <div className={styles.paymentText}>
              {isPaymentLoading ? '正在处理支付...' : '支付完成'}
            </div>
            <div className={styles.paymentDesc}>
              请在微信中完成支付
            </div>
          </div>
        }
        closeOnAction={false}
        closeOnMaskClick={false}
        onClose={handleCloseModal}
        actions={
          !isPaymentLoading
            ? [
                {
                  key: 'close',
                  text: '关闭',
                  onClick: handleCloseModal,
                },
              ]
            : []
        }
      />
    </>
  );
};

export default observer(WechatJSAPIPay);

// 导出Hook形式的使用方式
export const useWechatJSAPIPay = () => {
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);
  const { openid, startAuth, isAuthorized } = useWechatAuth();

  const startPayment = async (orderInfo: {
    purse_detail_id?: string;
    order_id?: string;
    trade_no?: string;
    total_fee?: string;
    out_trade_no?: string;
    description?: string;
    amount?: number;
    attach?: string;
  }, paymentType: 'recharge' | 'general' = 'general'): Promise<any> => {
    if (!wechatJSAPIPayService.isWechatBrowser()) {
      throw new Error('请在微信中打开');
    }

    setIsPaymentLoading(true);

    try {
      // 确保已授权
      let currentOpenid = openid;
      if (!isAuthorized) {
        currentOpenid = await startAuth();
      }

      if (paymentType === 'recharge') {
        if (!orderInfo.purse_detail_id || !orderInfo.total_fee) {
          throw new Error('充值订单信息不完整');
        }

        const rechargeData = {
          purse_detail_id: orderInfo.purse_detail_id,
          total_fee: orderInfo.total_fee,
          openid: currentOpenid,
        };

        const result = await wechatRechargePayService.executeRechargePay(rechargeData);

        if (result.success) {
          return result;
        } else if (result.cancelled) {
          throw new Error('用户取消支付');
        } else {
          throw new Error(result.message || '支付失败');
        }
      } else {
        // 订单支付
        const tradeNo = orderInfo.order_id || orderInfo.trade_no;
        if (!tradeNo || !orderInfo.total_fee) {
          throw new Error('订单支付信息不完整');
        }

        const orderData = {
          trade_no: tradeNo,
          total_fee: orderInfo.total_fee,
          openid: currentOpenid,
        };

        const result = await wechatOrderPayService.executeOrderPay(orderData);

        if (result.success) {
          return result;
        } else if (result.cancelled) {
          throw new Error('用户取消支付');
        } else {
          throw new Error(result.message || '支付失败');
        }
      }
    } finally {
      setIsPaymentLoading(false);
    }
  };

  return {
    startPayment,
    isPaymentLoading,
    isAuthorized,
  };
};
