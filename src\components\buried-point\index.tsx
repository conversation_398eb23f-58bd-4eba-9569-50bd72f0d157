import React from 'react';
import Config from './buriedPoint.config';
import { clientSdk } from '@/apis';

interface IProps {
  sensors: {
    eventName: string;
    data?: any;
  };
  data?: any;
  onClick?: Function;
  children: React.ReactElement;
}

/**
 * 合并属性，如果有相同的方法，先执行props上的方法，然后再执行children的方法
 * @param childrenProps
 * @param props
 */
function mergeProps(childrenProps, props) {
  const mergedProps = { ...props };
  Object.entries(props).forEach(([key, value]) => {
    if (key in childrenProps) {
      if (typeof value === 'function') {
        mergedProps[key] = function (...params) {
          childrenProps[key](...params);
          value(...params);
        };
      } else {
        mergedProps[key] = childrenProps[key];
      }
    }
  });
  return mergedProps;
}

function BuriedPoint({ data, sensors, children, ...otherProps }: IProps) {
  if (!children) {
    return null;
  }
  const mergedProps = mergeProps(children.props, otherProps);
  return React.cloneElement(children, {
    ...mergedProps,
    onClick: function (...params) {
      if (children.props.onClick) {
        children.props.onClick(...params);
      }
      if (otherProps.onClick) {
        otherProps.onClick(...params);
      }
      if (sensors) {
        clientSdk.buriedPoint(sensors.eventName, sensors.data);
      }
    },
  });
}

BuriedPoint.Config = Config;

export default BuriedPoint;
