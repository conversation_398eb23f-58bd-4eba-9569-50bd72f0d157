{"name": "ziniao-lite", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev:h5": "cross-env SUPER_CLIENT=H5 vite --host --mode dev", "dev:ios": "cross-env SUPER_CLIENT=iOS vite --host --mode dev", "dev:android": "cross-env SUPER_CLIENT=Android vite --host --mode dev", "build": "cross-env SUPER_CLIENT=Android vite build", "build:ios": "cross-env SUPER_CLIENT=iOS vite build", "build:exposes": "vite build --config vite.exposes.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "i18n:extract": "node ./scripts/i18n/extract.cjs", "i18n:sync": "node ./scripts/i18n/sync.cjs"}, "dependencies": {"@bit/greatfed.superbrowser.superudesk": "^0.0.9", "@ziniao-fe/components": "^1.1.14", "@ziniao-fe/core": "^0.2.15", "@ziniao-fe/login": "1.0.23", "ahooks": "^3.7.11", "antd-mobile": "^5.36.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.6.8", "classnames": "^2.5.1", "dayjs": "^1.11.11", "dayjs-plugin-utc": "^0.1.2", "di18n-react": "^0.1.25", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mathjs": "11.11.2", "mitt": "^3.0.1", "mobx": "^6.12.3", "mobx-react": "^9.1.1", "nanoid": "^5.0.7", "query-string": "^9.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-icons": "^5.2.1", "react-image-lightbox": "^5.1.4", "react-photo-view": "^1.2.7", "react-router-dom": "^6.23.1", "react-svg": "^16.1.34", "react-transition-group": "^4.4.5", "react-window": "^1.8.11", "ua-parser-js": "^2.0.0-beta.1"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.197", "@types/node": "^20.5.7", "@types/pako": "^2.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-transition-group": "^4.4.11", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "antd": "^5.21.6", "autoprefixer": "^10.4.15", "cross-env": "^7.0.3", "di18n-cli": "^0.1.29", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "postcss-preset-env": "^9.5.13", "postcss-px-to-viewport": "^1.1.1", "react-dev-inspector": "^1.9.0", "sass": "^1.77.5", "shelljs": "^0.8.5", "terser": "^5.37.0", "typescript": "^5.4.5", "vconsole": "^3.15.1", "vite": "^5.2.11", "vite-plugin-babel": "^1.2.0", "vite-plugin-pages": "^0.32.1", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vue-template-compiler": "^2.7.15"}, "resolutions": {"react-error-overlay": "6.0.11"}}