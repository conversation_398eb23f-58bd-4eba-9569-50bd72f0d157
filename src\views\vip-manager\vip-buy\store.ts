import _ from 'lodash';
import { observable, action, computed, makeAutoObservable } from 'mobx';
import { VIPBuyType, PayStep, SuperviseType, PaymentTypes } from './const';
import { vipBuySensorHelper } from './utils';
import { computeSinglePrice, computeTotalPrice, ComputeTotalPriceType } from './price-compute';
import { BuyVipSourceSeat } from './type';
import { urlTool } from '@/utils';
import RootStore from '@/stores';
import { to } from '@ziniao-fe/core';
import VipService from '@/services/vip-manage';
import { userService } from '@/services';
import { PAY_METHODS } from '@/components/pay-methods/const';
import deviceService from '@/services/device';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import BuriedPoint from '@/components/buried-point';

class Store {
  // 购买类型
  @observable VIPBuyType: VIPBuyType = VIPBuyType.Buy;

  // 监管类型
  @observable superviseType: SuperviseType = SuperviseType.acc;

  @observable payModalVisible = false;

  // 支付方式
  @observable paymentTypes: PAY_METHODS = PAY_METHODS.ALI;

  // 支付步骤
  @observable payStep = PayStep.Package;

  // 是否选中七天滚动存储
  @observable rollingStorageIsSelected: boolean = false;

  // 价格来源
  @observable priceSource: ComputeTotalPriceType = {
    basePrice: 0,
    rollingStorageUnitPrice: 0,
    rollingStorageNums: 0,
    rollingStorageStep: 0,
    memberNums: 0,
    memberUnitPrice: 0,
    accNums: 0,
    accUnitPrice: 0,
  };

  @observable isOrderDetailVisible = false;

  /**
* @description 余额管理
* @memberof BaseStore
*/
  balanceManager: BalanceManger = {
    data: null,
    loading: false,
    error: '',
    isUsing: false,
  };

  // 订单id
  @observable orderId: number | null = null;
  @observable createOrderLoading = false;

  // 自动续费
  @observable isAutoRenew = false;

  // 加购套餐
  @observable expandPackage: {
    account?: VipAPI.ExpandPackage;
    member?: VipAPI.ExpandPackage;
    review_day?: VipAPI.ExpandPackage;
    loading: boolean;
  } = {
      loading: false,
      // 监管店铺
      account: undefined,
      // 监管成员
      member: undefined,
      // 存储天数
      review_day: undefined,
    };

  // 加购套餐
  @observable predictAddNumData = {
    store_count: 0,
    user_count: 0,
    origin_store_count: 0,
    origin_user_count: 0,
  };

  @observable selectedPackageId: number | null = null;
  @observable packageLoading = false;
  @observable allPackageData: VipAPI.VipPackage[] = [];

  @observable lasterRenewInfo: any = null;
  @observable sourceSeat: BuyVipSourceSeat = urlTool.getQueryString(
    'sourceSeat',
  ) as BuyVipSourceSeat;
  @observable sourceGroup = urlTool.getQueryString('source_group');
  @observable pageWhere: string = urlTool.getQueryString('page_where') || BuriedPoint.Config.VIP.PAGE_NAME.CLIENT;

  constructor() {
    makeAutoObservable(this);
  }

  // 选中的套餐
  @computed get selectedPackage() {
    return this.allPackageData?.find((item) => item.id === this.selectedPackageId);
  }

  // 加购名额倍数
  @computed get addSeatMultiple(): number {
    return ((this.selectedPackage?.period || 0) / 30) || 1;
  }

  // 计算优惠后的总价
  @computed get totalPrice() {
    const params = {
      ...this.priceSource,
      vipBuyType: this.VIPBuyType as VIPBuyType,
      rollingStorageIsSelected: this.rollingStorageIsSelected,
      expiry: (RootStore.instance.userStore?.vipInfo?.expiry || 0) * 1000,
      serverTime: RootStore.instance.systemStore.serverTime,
      superviseType: this.superviseType,
      addSeatMultiple: this.addSeatMultiple
    };
    return computeTotalPrice(params);
  }

  @computed get membersPrice() {
    let nums = this.priceSource.memberNums;
    if (this.superviseType !== SuperviseType.member) {
      nums = 0;
    }
    return computeSinglePrice({
      unitPrice: this.priceSource.memberUnitPrice,
      nums: nums,
      type: this.VIPBuyType,
      expiry: (RootStore.instance.userStore?.vipInfo?.expiry || 0) * 1000,
      serverTime: RootStore.instance.systemStore.serverTime,
      addSeatMultiple: this.addSeatMultiple
    });
  }

  @computed get accPrice() {
    let nums = this.priceSource.accNums;
    if (this.superviseType !== SuperviseType.acc) {
      nums = 0;
    }
    const price = computeSinglePrice({
      unitPrice: this.priceSource.accUnitPrice,
      nums: nums,
      type: this.VIPBuyType,
      expiry: (RootStore.instance.userStore?.vipInfo?.expiry || 0) * 1000,
      serverTime: RootStore.instance.systemStore.serverTime,
      addSeatMultiple: this.addSeatMultiple
    });
    return price;
  }

  @computed get discountPrice() {
    const basePrice = Number(
      ((this.selectedPackage?.price || 0) - (this.selectedPackage?.discount_price || 0)).toFixed(2),
    );
    const reviewDayPrice = Number(
      (
        (this.expandPackage?.review_day?.price || 0) -
        (this.expandPackage?.review_day?.discount_price || 0)
      ).toFixed(2),
    );
    const accountPrice = Number(
      ((this.expandPackage?.account?.price || 0) - (this.expandPackage?.account?.discount_price || 0)).toFixed(2),
    );
    const memberPrice = Number(
      ((this.expandPackage?.member?.price || 0) - (this.expandPackage?.member?.discount_price || 0)).toFixed(2),
    );
    const params = {
      ...this.priceSource,
      basePrice: basePrice,
      rollingStorageUnitPrice: reviewDayPrice,
      memberUnitPrice: memberPrice,
      accUnitPrice: accountPrice,
      vipBuyType: this.VIPBuyType,
      rollingStorageIsSelected: this.rollingStorageIsSelected,
      expiry: (RootStore.instance.userStore?.vipInfo?.expiry || 0) * 1000,
      serverTime: RootStore.instance.systemStore.serverTime,
      superviseType: this.superviseType,
      addSeatMultiple: this.addSeatMultiple
    };
    return computeTotalPrice(params);
  }

  // 优惠前的总价
  @computed get allTotalPrice() {
    return Number((this.totalPrice + this.discountPrice).toFixed(2));
  }

  @computed get rollingStoragePrice() {
    let nums = this.priceSource.rollingStorageNums;
    if (!this.rollingStorageIsSelected) {
      nums = 0;
    }
    return computeSinglePrice({
      unitPrice: this.priceSource.rollingStorageStep * this.priceSource.rollingStorageUnitPrice,
      nums: nums,
      type: this.VIPBuyType,
      expiry: (RootStore.instance.userStore?.vipInfo?.expiry || 0) * 1000,
      serverTime: RootStore.instance.systemStore.serverTime,
      addSeatMultiple: this.addSeatMultiple
    });
  }


  @action
  init = async () => {
    this.payStep = PayStep.Package;
    await RootStore.instance?.userStore?.getUserInfoNew();
    this.initVipBuyType();
    await Promise.all([this.getExpandPackage(), this.getPackageData()]);
    this.setSuperviseType(RootStore.instance?.userStore.getVipStatus.superviseType);
    this.initRenewScalingData();
    this.initPredictAddNumData();
    RootStore.instance?.userStore?.initVipAd();
    this.getBalance();
    // vipBuySensorHelper?.viewVipPage(this.VIPBuyType, this.sourceSeat, this.sourceGroup, this.pageWhere);
  };

  @action
  initVipBuyType = async () => {
    const userStore = RootStore.instance.userStore;
    const type = urlTool.getQueryString('type') as VIPBuyType;

    if (userStore?.getVipStatus?.isVipMember && type !== VIPBuyType.Scaling) {
      this.VIPBuyType = VIPBuyType.Renew;
      return;
    }

    if (!userStore?.getVipStatus?.isVipMember) {
      this.VIPBuyType = VIPBuyType.Buy;
      return;
    }
    this.VIPBuyType = type;
  };

  @action
  initRenewScalingData = async () => {
    if (this.VIPBuyType === VIPBuyType.Renew) {
      this.isAutoRenew = RootStore.instance.userStore?.getVipStatus?.isAutoRenew;
      const [err, data] = await to(VipService.getRenewOrderInfo());
      if (err) return;
      this.lasterRenewInfo = data;
      this.priceSource.accNums = data?.supervise?.account;
      this.priceSource.memberNums = data?.supervise?.member;
      this.priceSource.rollingStorageStep = data?.supervise?.review_day;

      if (data?.supervise?.review_day > data?.package?.review_day) {
        this.rollingStorageIsSelected = true;
      }
      const selectedId = data?.package?.member_package_id;
      this.setSelectedPackageId(selectedId);
      return;
    }

    if (this.VIPBuyType === VIPBuyType.Scaling) {
      this.isAutoRenew = RootStore.instance.userStore?.getVipStatus.isAutoRenew;
      const acc = urlTool.getQueryString('acc');
      const member = urlTool.getQueryString('member');
      if (this.superviseType === SuperviseType.acc) {
        this.priceSource.accNums = Number(acc || 1);
      } else if (this.superviseType === SuperviseType.member) {
        this.priceSource.memberNums = Number(member || 1);
      }
      const [err, data] = await to(VipService.getRenewOrderInfo());
      if (err) return;
      this.setSelectedPackageId(data?.package?.member_package_id);
      return;
    }

    if (this.VIPBuyType === VIPBuyType.Buy) {
      const data = this.allPackageData[0];
      this.setSelectedPackageId(data?.id as number);
      return;
    }
  };

  @action
  initPredictAddNumData = async () => {
    const [err, data] = await to(VipService.memberPredictAddNum({ predict_type: this.VIPBuyType }));
    if (err) return;
    this.predictAddNumData = data;
    return data;
  }

  @action
  changePriceSource = async (key: keyof ComputeTotalPriceType, value: number) => {
    this.priceSource[key] = value as never;
  };

  @action
  setrollingStorageChecked = async (isCheck: boolean) => {
    this.rollingStorageIsSelected = isCheck;
  };

  @action
  setPaymentType = async (val: PAY_METHODS) => {
    this.paymentTypes = val;
  };

  /**
* @description 是否能使用余额支付
* @memberof BaseStore
*/
  @computed
  get isAvailableBalance(): boolean {
    return isNaN(this.totalPrice)
      ? true
      : Number(this.balanceManager.data?.balance ?? 0) >= this.totalPrice;
  }

  /**
   * @description 自动选择支付方式
   * @memberof BaseStore
   */
  @action
  autoSetPaymentMethod = () => {
    const userStore = RootStore?.instance?.userStore;
    if (this.isAvailableBalance) {
      // 有余额时默认选中余额支付
      this.setPaymentType(PAY_METHODS.BALANCE);
    } else if (
      // 当前选中的是余额 
      this.paymentTypes === PAY_METHODS.BALANCE
    ) {
      // 如果余额不够，并且之前选中的是余额 则默认使用支付宝
      this.setPaymentType(userStore?.hasCreditPay ? PAY_METHODS.CREDIT : PAY_METHODS.ALI);
    } else if (this.paymentTypes !== PAY_METHODS.CREDIT && userStore?.hasCreditPay) {
      /** 余额不足且开启信用余额支付时，默认使用信用余额支付 */
      this.setPaymentType(PAY_METHODS.CREDIT);
    }
  };

  @action
  setSuperviseType = (val: SuperviseType) => {
    if (this.VIPBuyType === VIPBuyType.Buy) {
      this.superviseType = SuperviseType.acc;
      return;
    }
    this.superviseType = val;
  };

  @action
  setSelectedPackageId = (id: number | null) => {
    this.selectedPackageId = id;
    const selectedData = this.allPackageData.find((item) => item.id === id)
    this.priceSource.basePrice = selectedData?.discount_price || 0;
  };

  @action
  setPayStep = async (val: PayStep) => {
    this.payStep = val;
  };

  @action
  setVipBuyType = async (val: VIPBuyType) => {
    this.VIPBuyType = val;
  };

  @action
  setIsAutoRenew = async (val: boolean) => {
    this.isAutoRenew = val;
  };

  @action
  setIsOrderDetailVisible = async (val: boolean) => {
    this.isOrderDetailVisible = val;
  };

  @action
  setPayModalVisible = async (val: boolean) => {
    this.payModalVisible = val;
  };

  @action
  setOrderId = async (id: number | null) => {
    this.orderId = id;
  };

  // 获取加购套餐
  @action
  getExpandPackage = async () => {
    this.expandPackage.loading = true;
    const [err, data] = await to(VipService.expandPackage());
    this.expandPackage.loading = false;
    if (err) return;
    if (data?.account) {
      this.expandPackage.account = data?.account;
      this.priceSource.accUnitPrice = data?.account?.discount_price;
    }
    if (data?.member) {
      this.expandPackage.member = data?.member;
      this.priceSource.memberUnitPrice = data?.member?.discount_price;
    }
    if (data?.review_day?.id) {
      this.expandPackage.review_day = data?.review_day;
      this.priceSource.rollingStorageUnitPrice = data?.review_day?.discount_price;
      this.priceSource.rollingStorageNums = data?.review_day?.step;
      this.priceSource.rollingStorageStep = data?.review_day?.option?.options[0].step;
    }
  };

  // 获取套餐
  @action
  getPackageData = async () => {
    this.packageLoading = true;
    const [err, data] = await to(VipService.packageData());
    this.packageLoading = false;
    if (err) return;
    this.allPackageData = data?.list;
  };

  /**
 * @description 获取余额
 * @memberof BaseStore
 */
  @action
  getBalance = async () => {
    if (this.balanceManager.loading) {
      return;
    }
    this.balanceManager.loading = true;
    const [err, response] = await to(userService.getPurseBalance());
    if (err) {
      this.balanceManager.error = '获取余额失败';
    } else {
      this.balanceManager.data = response;
    }
    this.balanceManager.loading = false;
  };

  // 创建订单
  @action
  createOrder = async () => {
    try {
      this.createOrderLoading = true;

      const supervisePackage = [
        {
          package_id: this.expandPackage?.member?.id,
          count: this.superviseType === SuperviseType.member ? this.priceSource.memberNums : 0,
        },
        {
          package_id: this.expandPackage?.account?.id,
          count: this.superviseType === SuperviseType.acc ? this.priceSource.accNums : 0,
        },
      ];

      const params: any = {
        member_package: null,
        pay_price: {
          total: this.allTotalPrice,
          discount: this.discountPrice,
          paid: this.totalPrice,
        },
        pay_method: this.paymentTypes,
        auto_renew: this.isAutoRenew,
        type: this.VIPBuyType,
        source_seat: BuriedPoint.Config.VIP.PAGE_VIEWS.BUY_SOURCE_SEAT[this.sourceSeat] || this.sourceSeat,
        source_group: this.sourceGroup,
        page_where: this.pageWhere,
      };

      params.supervise_package = supervisePackage;

      if (this.VIPBuyType !== VIPBuyType.Scaling) {
        params.member_package = {
          package_id: this.selectedPackageId,
          count: 1,
        };
      }

      if (params.pay_method === PAY_METHODS.BALANCE) {
        params.pay_method = PaymentTypes.Balance;
      } else if (params.pay_method === PAY_METHODS.WECHAT) {
        params.pay_method = PaymentTypes.Wechat;
      } else if (params.pay_method === PAY_METHODS.ALI) {
        params.pay_method = PaymentTypes.Ali;
      }

      const [err, data] = await to(VipService.createOrder(params));

      if (err) return err;
      this.orderId = data?.order_id;

      // vipBuySensorHelper.buyVip({
      //   buy_period: this.selectedPackage?.period || 0,
      //   order_id: data?.order_id,
      //   pay_method: params.pay_method,
      //   sourceSeat: this.sourceSeat,
      //   type: this.VIPBuyType,
      //   sourceGroup: this.sourceGroup,
      //   pageWhere: this.pageWhere,
      // });
    } catch (error) {
      //
    } finally {
      this.createOrderLoading = false;
    }
  };

  @action
  onPay = async () => {
    if (this.paymentTypes == PAY_METHODS.BALANCE) return;
    let result;
    let params = {
      trade_no: this.orderId,
      total_fee: this.totalPrice?.toFixed(2),
    };
    const [err, res] = await to(this.paymentTypes == PAY_METHODS.ALI ? deviceService.onAliPay(params) : deviceService.onWeChatPay(params));
    if (!err) {
      if (this.paymentTypes == PAY_METHODS.ALI) {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientAliPay(res?.url);
      } else {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientWechatPay(res);
      }
    }
    return result;
  }
}

export default Store;
