import { computed, makeObservable, observable } from 'mobx';
import { type LoginService } from '@ziniao-fe/core';
import AndroidSdk from './android';
import iOSSdk from './ios';
import MiniProgramSdk from './mini-program';
import { isMiniProgram, isH5Program } from '@/utils/platform';
import H5ProgramSdk from './h5';

type IMessageListener = (...args: any[]) => any;
interface ClientSetStorageOptions {
  /** 会话级 */
  session?: boolean;
  /** 该缓存过期时间 */
  expireTime?: null | number;
}

/** 广播类型，安卓需要Module/Action，iOS只需要一个事件 */
type TupleBroadcastArgs = [string, string, IMessageListener] | [string, IMessageListener];

/**
 * 公共的sdk
 * @description hybrid web app运行必须依赖到的
 */
class SDK {
  /** 创建客户端适配层工厂函数 */
  static createAdapterFactory() {
    if (isH5Program()) {
      return new H5ProgramSdk();
    }
    if (isMiniProgram()) {
      return new MiniProgramSdk();
    }
    if (__IOS_CLIENT__) {
      return new iOSSdk();
    }

    return new AndroidSdk();
  }

  /** 客户端适配层实例 */
  clientSdkAdapter = SDK.createAdapterFactory();

  constructor() {
    makeObservable(this, {
      ready: computed,
      clientSdkAdapter: observable,
    });
  }
  /** 注册广播回调 */
  registerBroadcast = (...args: TupleBroadcastArgs) => {
    // @ts-ignore
    return this.clientSdkAdapter?.registerBroadcast(...args);
  };

  /** 移除广播回调 */
  removeBroadcast = (...args: TupleBroadcastArgs) => {
    // @ts-ignore
    return this.clientSdkAdapter?.removeBroadcast(...args);
  };
  /**
   * 当前客户端准备就绪
   * @computed
   */
  get ready() {
    return this.clientSdkAdapter.connected;
  }

  invoke<T = any>(...args: any[]) {
    // @ts-ignore
    return this.clientSdkAdapter.invoke<T>(...args);
  }
  /** 获取客户端配置 */
  getClientConfig() {
    return this.clientSdkAdapter?.getClientConfig();
  }
  getVersionInfo() {
    return this.clientSdkAdapter.getVersion();
  }
  /** 获取机器码信息、mac地址 */
  getMachineInfo() {
    return this.clientSdkAdapter?.getMachineInfo();
  }
  /** 获取公共属性 */
  getPublicProperties() {
    return this.clientSdkAdapter?.getPublicProperties();
  }
  /** 通用埋点（Android\iOS\H5） */
  buriedPoint(eventName: string, params: Record<string, any> = {}) {
    console.log('[buriedPoint]->[eventName]:%s->[params]:%o ', eventName, params);
    return this.clientSdkAdapter?.buriedPoint(eventName, params);
  }
  /**
   * @name 读客户端缓存
   * @param key string
   * @param session boolean
   */
  async getStorage<T = any>(key: string, session = false) {
    return this.clientSdkAdapter.getStorage<T>(key, session);
  }
  /**
   * @name 写入客户端缓存
   * @param key string
   * @param value any
   * @param options session: boolean 客户端启动生命周期的会话缓存，重启就丢失 expireTime这个值的过期时间
   * @returns boolean
   */
  async setStorage(
    key: string,
    value: any,
    options: ClientSetStorageOptions = {
      session: false,
      expireTime: null,
    }
  ) {
    return this.clientSdkAdapter.setStorage(key, value, options);
  }
  /** 获取登录信息 */
  getLoginInfo() {
    return this.clientSdkAdapter?.getLoginInfo();
  }
  /** 登出 */
  logout(params: SuperClient.LogoutParams, _options?: Partial<SuperClient.LogoutOptions>) {
    const options: SuperClient.LogoutOptions = {
      manual: false,
      backLoginView: true,
      ..._options,
    };

    return this.clientSdkAdapter?.logout(params, options);
  }
  /** 监听用户登录状态变化 */
  onUserChange(callback: (loginInfo: LoginService.LoginUserInfo | null) => void) {
    return this.clientSdkAdapter?.onUserChange(callback);
  }
}

const sdk = new SDK();

export default sdk;
