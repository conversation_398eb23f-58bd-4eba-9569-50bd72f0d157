import React, { useCallback } from 'react';
import { observer } from 'mobx-react';
import { Card, Checkbox, Switch, Modal, Ellipsis } from 'antd-mobile';
import CardItem from '@/components/card-item';
import { RightOutline } from 'antd-mobile-icons';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils';
import ClientRouter from '@/base/client/client-router';
import { SECURITY_ROUTER } from '@/constants';
import { SuperviseType } from '@/views/security/enum';
import useVipModals from '@/hooks/useVipModals';

import styles from './styles.module.scss';
interface SuperviseListItemProps {
  data: SuperviseModule.SuperviseMemberRow;
  key: number;
  isEnable: boolean;
  onFresh: () => void;
}
export const SuperviseListItem: React.FC<SuperviseListItemProps> = (record) => {
  
  const clientRouter = ClientRouter.getRouter();
  const { isEnable, onFresh } = record;
  const [superviseChecked, setSuperviseChecked] = React.useState(isEnable);
  const { username, name, records, is_record_file, id, role_name } = record.data;
  const { showVipModal } = useVipModals();
  const handleSwitchChange = useCallback(
    async (checked) => {
      await new Promise(async (resolve, reject) => {
        const commonToggleService = async () =>
          await to(superviseService.superviseUserToggle({ enable: checked, user_ids: [id] }));
        if (!checked) {
          setSuperviseChecked(false);
          await Modal.alert({
            title: '关闭事中监管',
            content: '是否立即关闭所选成员的事中监管？',
            showCloseButton: true,
            confirmText: '确定',
            onConfirm: async () => {
              const [err, res] = await commonToggleService();
              if (err) {
                setSuperviseChecked(true);
                resolve(false);
                return;
              }
              onFresh();
              resolve(true);
            },
            onClose: () => {
              setSuperviseChecked(true);
              resolve(false);
            },
          });
          return;
        }
        if (checked) {
          setSuperviseChecked(true);
          const [err, res] = await commonToggleService();
          if (err) {
            if (err) {
              showVipModal(() => {
                setSuperviseChecked(false);
                resolve(false);
              });
              return;
            }
          }
          onFresh();
          resolve(true);
        }
      });
    },
    [onFresh]
  );
  return (
    <Card className={styles.superviseListItem} key={id}>
      <div className={styles.header}>
        <Checkbox value={id}>
          <span className={styles.title}>
            <Ellipsis direction="end" content={`${username}(${name})`} />
          </span>
        </Checkbox>
        <Switch
          checked={superviseChecked}
          onChange={handleSwitchChange}
          uncheckedText="未监管"
          checkedText="监管中"
        />
      </div>
      <div className={styles.content}>
        <CardItem contentAlign="left" label="成员角色" content={role_name} />
        {isEnable && (
          <CardItem
            contentAlign="left"
            label="传输文件记录"
            content={is_record_file ? '已开启' : '未开启'}
          />
        )}
        {records > 0 && (
          <div
            onClick={() =>
              clientRouter.push(
                `${SECURITY_ROUTER.LOGS_SUPERVISE}/${id}?type=${
                  SuperviseType.Member
                }&keyword=${encodeURIComponent(username)}`
              )
            }
          >
            <CardItem
              contentAlign="left"
              label="监管日志"
              content={
                <span>
                  {records}
                  <RightOutline />
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default observer(SuperviseListItem);
