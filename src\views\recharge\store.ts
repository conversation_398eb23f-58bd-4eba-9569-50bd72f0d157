import { makeAutoObservable, runInAction } from 'mobx';
import rechargeService from '@/services/todo/recharge';
import { to } from '@ziniao-fe/core';
import { PAY_METHODS } from '@/components/pay-methods/const';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import { Toast } from 'antd-mobile';
import { wechatRechargePayService } from '@/services/wechat-pay/recharge';
import { detectWechatAuthParams } from '@/utils/wechat-pay-utils';
import { isH5Program } from '@/utils/platform';

class Store {
  loading = false;
  amount = '';
  customAmount = '';
  visible = false;
  balance = 0;
  personCerticalVisible = false;
  isRecertification = false;
  orderId = '';

  constructor() {
    makeAutoObservable(this);
    this.init();
  }

  init = async () => {
    await this.getBalance();
  };

  setAmount = (val) => {
    runInAction(() => {
      this.amount = val;
    });
  };

  setCustomAmount = (val) => {
    runInAction(() => {
      this.customAmount = val;
    });
  };

  setVisible = (val) => {
    runInAction(() => {
      this.visible = val;
    });
  };
  setPersonCerticalVisible = (val) => {
    runInAction(() => {
      this.personCerticalVisible = val;
    });
  };
  setIsRecertification = (val) => {
    runInAction(() => {
      this.isRecertification = val;
    });
  };

  setBalance = (val) => {
    runInAction(() => {
      this.balance = val;
    });
  };

  setOrderId = (id) => {
    runInAction(() => {
      this.orderId = id;
    });
  };

  getBalance = async () => {
    runInAction(() => {
      this.loading = true;
    });
    const [err, response] = await to<any>(rechargeService.getBalance());
    if (err) return;
    this.setBalance(response.balance);
    runInAction(() => {
      this.loading = false;
    });
  };

  onGenerateOrder = async (payMethod) => {
    const data = {
      pay_money: this.amount,
      payment_method: payMethod,
    };
    const [err, response] = await to(rechargeService.createOrder(data));
    if (!err) {
      this.setOrderId(response?.purse_detail_id);
    } else if (err?.ret == 60004) {
      this.setPersonCerticalVisible(true);
    } else {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: err?.data?.msg,
      });
    }
    return !err;
  };

  onPay = async (payMethod) => {
    let result;

    // 微信公众号支付
    if (payMethod === PAY_METHODS.WECHAT_JSAPI) {
      return await this.handleWechatJSAPIPay();
    }

    // 传统支付方式
    let params = {
      purse_detail_id: this.orderId,
      total_fee: this.amount,
    };
    const [err, res] = await to(
      payMethod == PAY_METHODS.ALI
        ? rechargeService.onAliPay(params)
        : rechargeService.onWeChatPay(params)
    );
    if (!err) {
      if (payMethod == PAY_METHODS.ALI) {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientAliPay(res?.url);
      } else {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientWechatPay(res);
      }
    }
    return result;
  };

  /**
   * 处理微信公众号支付
   */
  handleWechatJSAPIPay = async () => {
    try {
      // 检查是否在微信浏览器中
      if (!isH5Program()) {
        Toast.show({
          icon: 'fail',
          content: '请在微信中打开',
        });
        return { isSuccess: false, msg: '请在微信中打开' };
      }

      // 获取用户openid
      let openid = detectWechatAuthParams().openid!;

      // 构建充值数据
      const rechargeData = {
        purse_detail_id: this.orderId,
        total_fee: this.amount,
        openid,
      };

      // 执行充值支付
      const result = await wechatRechargePayService.executeRechargePay(rechargeData);

      if (result.success) {
        return { isSuccess: true, msg: '充值成功' };
      } else if (result.cancelled) {
        return { isSuccess: false, msg: '支付已取消' };
      } else {
        return { isSuccess: false, msg: result.message || '充值失败' };
      }
    } catch (error) {
      console.error('微信公众号支付失败:', error);
      return {
        isSuccess: false,
        msg: error instanceof Error ? error.message : '支付失败',
      };
    }
  };
}

export default Store;
