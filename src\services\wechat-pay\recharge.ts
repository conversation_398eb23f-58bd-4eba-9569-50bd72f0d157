import { httpService } from '@/apis';
import { to } from '@/utils';
import { wechatJSAPIPayService } from './index';

/**
 * 微信公众号充值支付服务
 * 专门处理充值场景的微信支付
 */
class WechatRechargePayService {
  /**
   * 创建充值订单并获取支付参数
   * @param rechargeData 充值数据
   * @returns Promise<支付参数>
   */
  async createRechargeOrder(rechargeData: {
    purse_detail_id: string;
    total_fee: string;
    openid: string;
  }) {
    // 参数验证
    if (!rechargeData.purse_detail_id) {
      throw new Error('缺少充值订单ID');
    }
    
    if (!rechargeData.total_fee) {
      throw new Error('缺少充值金额');
    }
    
    if (!rechargeData.openid) {
      throw new Error('缺少用户openid');
    }

    // 验证金额格式
    const amount = parseFloat(rechargeData.total_fee);
    if (isNaN(amount) || amount <= 0) {
      throw new Error('充值金额无效');
    }

    if (amount > 10000) {
      throw new Error('单次充值金额不能超过10000元');
    }

    // 构建请求参数
    const orderData = {
      purse_detail_id: rechargeData.purse_detail_id,
      total_fee: rechargeData.total_fee,
      code: this.generateMachineString(), // 机器字符串
      matching_string: this.generateMatchingString(), // 匹配字符串
      user_id: this.getCurrentUserId(), // 用户ID
    };

    try {
      // 调用微信支付服务创建充值订单
      const payParams = await wechatJSAPIPayService.createRechargeOrder(orderData);
      
      return {
        ...payParams,
        orderInfo: {
          purse_detail_id: rechargeData.purse_detail_id,
          total_fee: rechargeData.total_fee,
          description: `账户充值 ¥${rechargeData.total_fee}`,
        },
      };
    } catch (error) {
      console.error('创建充值订单失败:', error);
      throw error;
    }
  }

  /**
   * 执行充值支付
   * @param rechargeData 充值数据
   * @returns Promise<支付结果>
   */
  async executeRechargePay(rechargeData: {
    purse_detail_id: string;
    total_fee: string;
    openid: string;
  }) {
    try {
      // 验证充值金额
      const validation = this.validateRechargeAmount(rechargeData.total_fee);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 1. 创建充值订单
      const orderResult = await this.createRechargeOrder(rechargeData);

      // 2. 调起微信支付
      const payResult = await wechatJSAPIPayService.invokeWechatPay(orderResult);

      // 3. 处理支付结果
      if (payResult.resultCode === 'SUCCESS') {
        // 支付成功，查询订单状态确认
        try {
          const orderStatus = await this.queryRechargeOrderStatus(rechargeData.purse_detail_id);
          return {
            success: true,
            message: '充值成功',
            orderInfo: orderResult.orderInfo,
            orderStatus,
          };
        } catch (queryError) {
          // 即使查询失败，也认为支付成功
          console.warn('查询充值订单状态失败:', queryError);
          return {
            success: true,
            message: '充值成功',
            orderInfo: orderResult.orderInfo,
          };
        }
      } else if (payResult.resultCode === 'CANCEL') {
        return {
          success: false,
          message: '用户取消充值',
          cancelled: true,
        };
      } else {
        throw new Error('充值失败');
      }
    } catch (error) {
      console.error('充值支付失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '充值失败，请重试',
        error,
      };
    }
  }

  /**
   * 查询充值订单状态
   * @param purseDetailId 充值订单ID
   * @returns Promise<订单状态>
   */
  async queryRechargeOrderStatus(purseDetailId: string) {
    return await wechatJSAPIPayService.queryRechargeStatus(purseDetailId);
  }

  /**
   * 获取充值历史记录
   * @param params 查询参数
   * @returns Promise<充值历史>
   */
  async getRechargeHistory(params: {
    page?: number;
    limit?: number;
    start_date?: string;
    end_date?: string;
  } = {}) {
    const [err, res] = await to(
      httpService<{
        list: Array<{
          purse_detail_id: string;
          amount: string;
          status: string;
          created_at: string;
          pay_time?: string;
          trade_no?: string;
        }>;
        total: number;
        page: number;
        limit: number;
      }>({
        url: '/purse/recharge/history',
        method: 'POST',
        data: {
          page: params.page || 1,
          limit: params.limit || 20,
          start_date: params.start_date,
          end_date: params.end_date,
        },
      })
    );

    if (err) {
      throw new Error('获取充值历史失败');
    }

    return res;
  }

  /**
   * 生成机器字符串
   * @returns string
   */
  private generateMachineString(): string {
    // 这里应该根据实际业务需求生成机器字符串
    // 暂时返回一个基于时间戳的字符串
    return `RECHARGE_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * 生成匹配字符串
   * @returns string
   */
  private generateMatchingString(): string {
    // 这里应该根据实际业务需求生成匹配字符串
    // 暂时返回一个基于时间戳的字符串
    return `MATCH_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * 获取当前用户ID
   * @returns string
   */
  private getCurrentUserId(): string {
    try {
      // 方法1: 从localStorage获取用户信息
      const userInfo = localStorage.getItem('user_info');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        if (user.user_id || user.id) {
          return user.user_id || user.id;
        }
      }

      // 方法2: 从sessionStorage获取
      const sessionUserInfo = sessionStorage.getItem('user_info');
      if (sessionUserInfo) {
        const user = JSON.parse(sessionUserInfo);
        if (user.user_id || user.id) {
          return user.user_id || user.id;
        }
      }

      // 方法3: 从全局状态管理获取（如果有的话）
      if (typeof window !== 'undefined' && (window as any).userStore) {
        const userStore = (window as any).userStore;
        if (userStore.userInfo?.user_id || userStore.userInfo?.id) {
          return userStore.userInfo.user_id || userStore.userInfo.id;
        }
      }

      // 如果都获取不到，抛出错误而不是返回unknown
      throw new Error('无法获取用户ID，请先登录');
    } catch (error) {
      console.error('获取用户ID失败:', error);
      throw new Error('无法获取用户ID，请先登录');
    }
  }

  /**
   * 验证充值金额
   * @param amount 金额
   * @returns 验证结果
   */
  validateRechargeAmount(amount: string | number): {
    isValid: boolean;
    error?: string;
  } {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount)) {
      return { isValid: false, error: '金额格式错误' };
    }
    
    if (numAmount <= 0) {
      return { isValid: false, error: '充值金额必须大于0' };
    }
    
    if (numAmount < 1) {
      return { isValid: false, error: '最小充值金额为1元' };
    }
    
    if (numAmount > 10000) {
      return { isValid: false, error: '单次充值金额不能超过10000元' };
    }
    
    // 检查小数位数
    const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
      return { isValid: false, error: '金额最多支持2位小数' };
    }
    
    return { isValid: true };
  }

  /**
   * 格式化充值金额
   * @param amount 金额
   * @returns 格式化后的金额字符串
   */
  formatRechargeAmount(amount: string | number): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount)) {
      return '0.00';
    }
    
    return numAmount.toFixed(2);
  }
}

// 导出单例
export const wechatRechargePayService = new WechatRechargePayService();
