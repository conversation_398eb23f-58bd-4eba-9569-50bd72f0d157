# 紫鸟移动端 web app

## 注意事项

1. \*.d.ts 中不能 import 任何其它例如枚举的对象，会改变所有类型的引用关系，造成网站崩溃 !!!

2. package.json 中的 type: "Module"会把所有 js 模块视为 ES module 对待，有些 npm scripts 中的相关文件，要兼容 CommonJS 规范，Node.js 将具有.cjs 扩展名的文件视为
   CommonJS 模块

3. IOS 调试走 `yarn dev:ios`

4. 项目样式高度适配，尽量避免直接使用 100vh, 因为 IOS 适配有上下安全区域的问题。可以使用`css变量：var(--safe-height)` 已做适配处理

## 调试方式

### Android

真机通过 USB 连接电脑，手机需要开启开发者模式，然后通过 chrome 的 `chrome://inspect/#devices`实现调试。

### IOS

调研并尝试过 N 款调试工具，总结：已经是 mac 机的开发者，直接使用 Xcode 用真机调试。windows 上使用`chii `插件。

#### 步骤

1. `npm install chii -g` or `yarn global add chii`

2. `chii start -p 8080`

3. 在根目录的 index.html 文件中添加 ` <script src="//host-machine-ip:8080/target.js"></script>`

4. 然后 windows 电脑上，输入 ip 地址 `host-machine-ip:8080`，即可实现远程调试移动端开发

## 路由适配（三端：Android、iOS、H5）统一适配，!!!之后路由跳转统一调用此处!!!

1. 基础通用调用

```javascript
import ClientRouter from '@/base/client/client-router';
const clientRouter = ClientRouter.getRouter();
clientRouter.goBack();
```

2. IOS、以及 Android 定制化调用（什么是定制化：就是你很明确这是 android 或者 IOS 会调用的，不明确的，就走通用第 1.）

```javascript
(clientSdk.clientSdkAdapter as iOSSdk).clientRouter.presentViewController({
      url: this.getHttpUrl(WORKBENCH_URL.LOGIN).url,
    });

```

3. 一些 H5 的 react-router-dom 相关的 hook 方法，还是可以按需使用。这适配方法，主要目的在于处理与原生端跳转逻辑的兼容性问题。

## 对外一致 URL 参数

```javascript
https://[test|sim|pro]mobile.ziniao.com/?code=[code]&token=[token]&loginType=[loginType]&env=[env]&machineString=[machineString]&openId=[openId]#/todo

 interface UrlParams {
  token?: string;
  openId?: string; //  微信小程序的openId
  loginType: string; // wx_mini_program | h5_program（公众号）
  env: string; // test | sim | production
  machineString: string;
  company_id?: string;
  company?:string; //同 company_id
  code?: string; // 微信公众号的code,公众号必传
}
```
## 埋点接入（Android、iOS、H5、微信小程序、微信公众号）
### 调用侧不用关心内部，统一适配了接口，调用如下

```javascript

import { clientSdk } from '@/apis';
clientSdk.buriedPoint(eventName: string, params: Record<string, any>)

```

## 版本更新记录

1. 时间：2025-04-02， 封版 ios-master（ios 主干分支），注意！！由于 ios 和 android 未合并代码，暂且在命令为 ios-master 不合并到 master。
