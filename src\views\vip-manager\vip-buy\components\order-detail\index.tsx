import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import HeaderNavbar from '@/components/header-navbar';
import Store from '../../store';
import { Button, Checkbox, Modal, Toast } from 'antd-mobile';
import styles from './style.module.scss';
import { useSearchParams } from 'react-router-dom';
import { isNaN } from 'lodash';
import PayMethods from '@/components/pay-methods';
import { PAY_METHODS } from '@/components/pay-methods/const';
import SuperToast from '@/components/super-toast';
import ClientRouter from '@/base/client/client-router';
import { CloseAliPayAndWxPay, isH5Program } from '@/utils/platform';
import { VIPBuyType } from '../../const';
import RootStore from '@/stores';
import timezone from 'dayjs/plugin/timezone';
import dayjsPluginUTC from 'dayjs/plugin/utc';
import dayjs from 'dayjs';
import SuperPopup from '@/components/super-popup';
import { APP_ROUTER } from '@/constants';
import setting from '@/views/setting';

dayjs.extend(dayjsPluginUTC);
dayjs.extend(timezone);

const beijingFormatTime = (timestamp: number | string, days?: number) => {
  return dayjs?.utc(timestamp)?.tz('Asia/Shanghai')?.add(days || 30, 'days')?.format('YYYY-MM-DD');
};

const getExpiredTime = (expiryTime: number, vipBuyType: VIPBuyType, expiryTimeStr: string, days?: number) => {
  const startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')?.split(' ')[0];
  const renewStartTime = expiryTimeStr?.split(' ')[0];

  if (vipBuyType === VIPBuyType.Buy) {
    return `${startTime} ~ ${beijingFormatTime(RootStore.instance?.systemStore?.serverTime, days)}`;
  }

  if (vipBuyType === VIPBuyType.Renew) {
    return `${renewStartTime} ~ ${beijingFormatTime(expiryTime * 1000, days)}`;
  }

  if (vipBuyType === VIPBuyType.Scaling) {
    return `${startTime} ~ ${renewStartTime}`;
  }

  return '--';
};


const OrderDetail: React.FC<{ store: Store }> = (props) => {
  const [searchParams] = useSearchParams();
  const userStore = RootStore.instance.userStore;
  const systemStore = RootStore.instance.systemStore;
  const [protocolChecked, setProtocolChecked] = useState(false);

  let query = searchParams.get('ids');
  const clientRouter = ClientRouter.getRouter();
  let ids = [];
  if (query) {
    ids = JSON.parse(window.atob(query || '') || '[]');
  }
  const store = props.store
  const {
    selectedPackage,
    totalPrice,
    discountPrice,
    allTotalPrice,
    createOrderLoading,
  } = store;

  useEffect(() => {
    if (store.balanceManager?.data) {
      store.autoSetPaymentMethod();
    }
  }, [store.balanceManager.data?.balance, store.totalPrice]);

  const paySuccessCallback = async () => {
    setTimeout(() => {
      userStore.getUserInfoNew();
    }, 2000);
    
    Toast.clear();
    Toast.show({
      icon: 'success',
      content: '支付成功',
      duration: 3000,
      maskClickable: false,
      afterClose: () => {
        clientRouter.goBack();
      },
    });
  };

  const payFailedCallback = async (data) => {
    Toast.clear();
    SuperToast.error(data?.msg, 2);
    store.setOrderId(null);
  };

  const handlePay = async () => {
    const balance = store?.balanceManager?.data?.balance;
    /**检测余额是否充足 */
    if (isH5Program()) {
      if (Number(balance || '0') < store.totalPrice) {
        Toast.show({
          icon: 'fail',
          content: '余额不足',
          duration: 3000,
        });
        return;
      }
    }
    Toast.show({
      icon: 'loading',
      content: '支付中...',
      duration: 0,
      maskClickable: false,
    });

    const errData = await store.createOrder();

    if (
      store.paymentTypes != PAY_METHODS.BALANCE &&
      store.paymentTypes != PAY_METHODS.CREDIT &&
      !errData
    ) {
      const result = await store.onPay();
      if (result?.isSuccess) {
        paySuccessCallback();
      } else {
        payFailedCallback(result);
      }
    } else {
      if (!errData) {
        paySuccessCallback();
      } else {
        payFailedCallback({ msg: errData?.message || '支付失败' });
      }
    }
  };

  const goBack = () => {
    clientRouter.goBack();
  };

  const handleServiseProtocol = () => {
    clientRouter.push(APP_ROUTER.VIP_BUY_SERVISE_PROTOCOL)
  }

  const handlePayProtocol = () => {
    Modal.alert({
      closeOnMaskClick: true,
      title: "付款须知",
      content: <div className={styles.protocolConent}>
        <div className={styles.text}>
          付款方承诺，付款方系紫鸟浏览器用户本人，如非本人，付款方与紫鸟浏览器用户共同作出以下代付安排。
        </div>
        <div className={styles.text}>
          紫鸟浏览器用户（“代付指示方”）委托付款方向福建紫讯代为支付代付指示方与福建紫讯之间的本次交易款项（“代付安排”）。代付指示方和付款方共同作出以下确认：
        </div>
        <div className={styles.text}>
          （1）本次交易款项是基于代付指示方与福建紫讯的真实交易。
        </div>
        <div className={styles.text}>
          （2）代付安排并非出于任何逃税及避税等违法税务相关法律法规的目的。
        </div>
        <div className={styles.text}>
          （3）付款方向福建紫讯支付的款项来源合法，并不涉及任何洗黑钱活动。
        </div>
        <div className={styles.text}>
          （4）委托代付的款项支付完毕后，付款方不会以任何理由要求福建紫讯返还相关款项。

        </div>
        <div className={styles.text}>
          （5）代付安排系代付指示方与付款方之间的约定，福建紫讯不需承担任何因代付安排而产生的纠纷、责任，将来若发生任何就代付安排而产生的纠纷、索赔，将由代付指示方承担任何损失。
        </div>
      </div>,
    })
  }

  return (
    <div>
      <SuperPopup
        title="订单确认"
        visible={store.isOrderDetailVisible}
        onClose={() => {
          store.setIsOrderDetailVisible(false);
        }}
        className={styles.popup}
      >
        <div className={styles.body}>
          {/* <div className={styles.top}>
            <HeaderNavbar title="订单确认" onBack={goBack}></HeaderNavbar>
          </div> */}
          <div className={styles.renewBox}>
            <div className={styles.container}>
              <div className={styles['container-title']}>订单价格</div>

              <div className={` ${styles['device-card']}`}>
                <div className={styles['info-box']}>
                  <div className={`${styles['item-title']} ${styles['price-font']}`}>
                    <div>商品名称</div>
                    <div className="black">{store.VIPBuyType === VIPBuyType.Scaling ? '事中监管名额' : '安全管家'}</div>
                  </div>
                  {store.VIPBuyType !== VIPBuyType.Scaling && (
                    <div className={`${styles['item-title']} ${styles['price-font']}`}>
                      <div>购买时长</div>
                      <div className="black">{selectedPackage?.period}天</div>
                    </div>
                  )}

                  <div className={`${styles['item-title']} ${styles['price-font']}`}>
                    <div>预计生效时间</div>
                    <div className="black">{getExpiredTime(userStore?.vipInfo?.expiry || 0, store.VIPBuyType, userStore?.vipInfo?.expiry_str || '', selectedPackage?.period)}</div>
                  </div>

                  <div className={`${styles['item-title']} ${styles['price-font']}`}>
                    <div>订单总价</div>
                    <div className="black">{systemStore.globalCurrency}{allTotalPrice?.toFixed(2)}</div>
                  </div>

                  <div className={`${styles['item-title']} ${styles['price-font']}`}>
                    <div>优惠金额</div>
                    <div className="black">{systemStore.globalCurrency}{discountPrice?.toFixed(2)}</div>
                  </div>

                </div>

                <div className={`${styles.amount} ${styles['price-font']}`}>
                  <div>应付金额</div>
                  <div>
                    <span className={styles['big-font']}>{systemStore.globalCurrency}{totalPrice?.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.container}>
              <div className={styles['container-title']}>支付方式</div>
              <div className={styles.payBox}>
                <PayMethods
                  getBalance={store.getBalance}
                  balance={store?.balanceManager?.data?.balance}
                  currentPayMethod={store.paymentTypes}
                  onChangePayMethods={(val) => {
                    store.setPaymentType(val);
                  }}
                  payMoney={store.totalPrice}
                />
              </div>
            </div>
          </div>

          <div className={styles['sure']}>
            <div>
              <div>
                应付金额：
                {!isNaN(totalPrice) ? (
                  <span className="color-danger" style={{ fontSize: '4.8vw' }}>
                    {systemStore.globalCurrency}{totalPrice?.toFixed(2)}

                  </span>
                ) : (
                  <span className="color-danger" style={{ fontSize: '4.8vw' }}>
                    -
                  </span>
                )}
              </div>
              {!isNaN(store.discountPrice) ? (
                <div className={styles['red-font']}>已优惠{systemStore.globalCurrency}{store.discountPrice?.toFixed(2)}</div>
              ) : (
                <div className={styles['red-font']}>已优惠-</div>
              )}
            </div>

            <Button
              size="large"
              block
              color="primary"
              loading={store.createOrderLoading}
              onClick={handlePay}
            >
              立即支付
            </Button>
          </div>
        </div>
      </SuperPopup>

      <Button
        className={styles['buy-btn']}
        style={{ background: '#D09E4D', color: '#fff' }}
        block
        onClick={() => {
          if (!protocolChecked) {
            Toast.show({ content: '请先勾选服务协议和付款协议' })
            return;
          }
          store.setIsOrderDetailVisible(true);
        }}>
        购买
      </Button>

      <div className={styles.protocol}>
        <Checkbox onChange={(checked) => setProtocolChecked(checked)} checked={protocolChecked}></Checkbox>
        <span className={styles.text1} style={{ marginLeft: '4px' }}>已同意</span>
        <span className={styles.text2} onClick={handleServiseProtocol}>服务协议</span>
        <span className={styles.text1}>和</span>
        <span className={styles.text2} onClick={handlePayProtocol}>付款协议</span>
      </div>
    </div>
  );
};
export default observer(OrderDetail);
