import { httpService } from '@/apis';

class SuperviseService {
  /** 已开启事中监管的账号列表 */
  superviseAcctList = (
    data: Partial<{
      page: number;
      limit: number;
      search: string;
      sort?: { field: string; order: string }[];
    }>
  ) => {
    return httpService({
      url: '/supervise/account/list',
      method: 'POST',
      data,
    });
  };
  /** 会员事中监管记录查看时长调整活动信息 */
  memberCenterEventData = () => {
    return httpService<any>({
      url: '/member/center/event_data',
      method: 'POST',
    });
  };
  /** 获取已使用的权益数 */
  getUsedBenefit = () => {
    return httpService<any>({
      url: '/member/center/used/benefit',
      method: 'POST',
    });
  };
  /** 账号开关事中监管 */
  toggleSuperviseAccount = (data: SuperviseModule.IParamsSupervisePageEnable) => {
    return httpService({
      url: '/supervise/account/toggle',
      method: 'POST',
      data,
    },{
      alertError: false,
    });
  };
  /** 成员开关事中监管 */
  superviseUserToggle = (data: { user_ids: number[]; enable: boolean }) => {
    return httpService({
      url: '/supervise/user/toggle',
      method: 'POST',
      data,
    });
  };
  /** 账号开关是否传输文件记录 */
  superviseAcctFileRecord = (data: { account_ids: number[]; enable: boolean }) => {
    return httpService({
      url: '/supervise/account/file_record/toggle',
      method: 'POST',
      data,
    });
  };
  /** 成员开关是否传输文件记录 */
  superviseUserFileRecord = (data: { staff_ids: number[]; enable: boolean }) => {
    return httpService({
      url: '/supervise/user/file_record/toggle',
      method: 'POST',
      data,
    });
  };
  /** 切换监管模式 */
  switchSuperviseType = (data: { supervise_type: number }) => {
    return httpService({
      url: '/member/center/switch_supervise_type',
      method: 'POST',
      data,
    });
  };
  /**
   * 设置公司事中监管相关配置
   * @param data
   * @returns
   */
  setSuperviseCompanyConfig(data: { is_show_tips: 0 | 1 }) {
    return httpService({
      url: '/supervise/set_company_config',
      method: 'POST',
      data,
    });
  }
  /** 已开启事中监管的成员列表 */
  superviseUserList = (
    data: Partial<{
      page: number;
      limit: number;
      search: string;
      department_ids?: number[];
      role_ids?: number[];
      is_enable?: boolean;
      sort?: { field: string; order: string }[];
    }>
  ) => {
    return httpService<SuperviseModule.SuperviseUserListResponse>({
      url: '/supervise/user/list',
      method: 'POST',
      data,
    });
  };
  /**
   * 获取帐号下拉列表
   */
  getAccountSelectList = (data: { only_auth?: boolean }) => {
    return httpService<any>({
      url: '/security/access_log/acct_list',
      method: 'POST',
      data,
    });
  };
  /** 获取账号授权成员列表 */
  getAccountAuthStaffList = (data: SuperviseModule.GetAuthStaffParams) => {
    return httpService<{ data: AccountService.AuthStaff[] }>({
      url: '/store/auth_staff/list',
      method: 'POST',
      data,
    });
  };
  /**
   * 监管日志列表
   */
  logList = (data: SuperviseModule.LogListParams) => {
    return httpService<any>({
      url: '/supervise/log',
      method: 'POST',
      data,
    });
  };
  /** 监管日志详情 */
  superviseLogDetail = (data: { session_id: string; event_id?: string }) => {
    return httpService({
      url: '/supervise/log_detail',
      method: 'POST',
      data,
    });
  };
}

export const superviseService = new SuperviseService();
