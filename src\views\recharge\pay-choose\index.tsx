import React, { FC } from 'react';
import styles from './styles.module.scss';
import { BsAlipay } from 'react-icons/bs';
import { RiWechatPayFill } from 'react-icons/ri';
import { observer } from 'mobx-react';
import { Image, Space, Toast } from 'antd-mobile';
import { PAY_METHODS } from '@/components/pay-methods/const';
import Store from '../store';
import SuperToast from '@/components/super-toast';
import creditPic from './images/ico-credit.png';
import RootStore from '@/stores';
import { isNil } from '@ziniao-fe/core';
import { CloseAliPayAndWxPay, isH5Program } from '@/utils/platform';

interface IProps {
  store: Store;
}

const PayChoose: FC<IProps> = (props) => {
  const { store } = props;
  const userStore = RootStore?.instance?.userStore;
  const paySuccessCallback = async (isCredit?) => {
    Toast.show({
      icon: 'success',
      content: '支付成功',
      duration: 3000,
      maskClickable: false,
    });
    store.getBalance();
    if (isCredit) {
      userStore.getCreditBalance();
    }
  };

  const payFailedCallback = async (data) => {
    SuperToast.error(data?.msg);
  };

  const onPay = async (method) => {
    store.setVisible(false);
    if (
      userStore?.hasCreditPay &&
      method == PAY_METHODS.RECHARGE_CREDIT &&
      Number(store.amount) >
        Number(userStore.userCreditManager?.info?.available_credit_balance || 0)
    ) {
      Toast.show({
        icon: 'fail',
        content: '信用余额不足',
        maskClickable: false,
      });
      return;
    }
    Toast.show({
      icon: 'loading',
      content: '支付中...',
      duration: 0,
      maskClickable: false,
    });
    const res = await store.onGenerateOrder(method);
    if (res) {
      if (method == PAY_METHODS.RECHARGE_CREDIT) {
        paySuccessCallback(true);
        Toast.clear();
        return;
      }
      const result = await store.onPay(method);
      Toast.clear();
      if (result?.isSuccess) {
        paySuccessCallback();
      } else {
        payFailedCallback(result);
      }
    } else {
      return;
    }
  };

  return (
    <div className={styles.payChoose}>
      {userStore?.hasCreditPay ? (
        <Space direction="vertical" block style={{ '--gap': '20px' }}>
          <div
            className={`${styles['item-title']}`}
            onClick={() => onPay(PAY_METHODS.RECHARGE_CREDIT)}
          >
            <div className={styles.paybox}>
              <div className={`${styles['icon-bg']} ${styles.ali}`}>
                <Image src={creditPic} className={styles['icon']} />
              </div>
              <div>
                信用余额:{' '}
                {`￥${
                  isNil(userStore.userCreditManager?.info?.available_credit_balance)
                    ? '-'
                    : userStore.userCreditManager?.info?.available_credit_balance?.toFixed?.(2)
                }`}
              </div>
            </div>
          </div>
        </Space>
      ) : (
        <div className={styles.payWrap}>
          <>
            {!CloseAliPayAndWxPay && (
              <div className={`${styles['item-title']}`} onClick={() => onPay(PAY_METHODS.ALI)}>
                <div className={styles.paybox}>
                  <div className={`${styles['icon-bg']} ${styles.ali}`}>
                    <BsAlipay className={styles['icon']} />
                  </div>
                  <div>支付宝</div>
                </div>
              </div>
            )}
            <div
              className={`${styles['item-title']}`}
              onClick={() => onPay(isH5Program() ? PAY_METHODS.WECHAT_JSAPI : PAY_METHODS.WECHAT)}
            >
              <div className={styles.paybox}>
                <div className={`${styles['icon-bg']} ${styles.wx}`}>
                  <RiWechatPayFill className={styles['icon']} />
                </div>
                <div>微信</div>
              </div>
            </div>
          </>
        </div>
      )}
    </div>
  );
};

export default observer(PayChoose);
