import { cloneDeep } from 'lodash/fp';

const config = {
  /** 会员 */
  VIP: {
    SOURCE_SEAT: {
      REGULATORY_SET: '移动端监管设置'
    },
    SOURCE_GROUP: {
      REGULATORY: '事中监管'
    },
    PAGE_NAME: {},
  },
};

(function convert(oldKeys, data: any) {
  Object.entries(data).forEach(([key, value]) => {
    if (typeof value === 'object') {
      convert([...oldKeys, key], value);
    } else {
      data[key] = [...oldKeys, key].join('.');
    }
  });
})([], cloneDeep(config));

export default config;
