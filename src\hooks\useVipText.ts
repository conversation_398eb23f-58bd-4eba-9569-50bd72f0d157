import React, { useState, useEffect } from 'react';
import RootStore from '@/stores';
import dayjs from 'dayjs';

const useVipText = () => {
  const {
    isVipTryout,
    isExpireding,
    isExpired,
    isAccSupervise,
    isVipMember,
  } = RootStore?.instance?.userStore?.getVipStatus;
  const vipAdInfo = RootStore?.instance?.userStore?.vipAdInfo as VipADInfo;
  const vipInfo = RootStore?.instance?.userStore?.vipInfo as VipInfo;
  const vipUsedInfo = RootStore?.instance?.userStore?.usedBenefit as VipADUsageInfo;
  const [vipText, setVipText] = useState<string>('');
  useEffect(() => {
    if (isVipTryout) {
      setVipText(`免费版可体验1个平台账号${vipAdInfo?.supervise_free_day ?? ''}天监管`);
    }
    if (isExpireding) {
      setVipText(`「安全管家」将于${dayjs(vipInfo?.expiry).format('YYYY-MM-DD')}到期`);
    }
    if (isExpired) {
      setVipText(`「安全管家」已到期，${isAccSupervise ? '账号' : '成员'}已失去监管`);
    }
    if (isVipMember) {
      setVipText(`「安全管家」当前可监管名额：
                 ${
                   isAccSupervise ? vipUsedInfo?.account_count || 0 : vipUsedInfo?.user_count || 0
                 } /
               ${
                 isAccSupervise
                   ? vipInfo?.vip_member_equity?.account || 0
                   : vipInfo?.vip_member_equity?.member || 0
               }个`);
    }
  }, [RootStore?.instance?.userStore?.getVipStatus, vipAdInfo, vipInfo, vipUsedInfo]);
  return vipText;
};
export default useVipText;
