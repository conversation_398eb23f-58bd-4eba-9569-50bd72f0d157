import React, { useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import { Toast } from 'antd-mobile';
import { wechatJSAPIPayService } from '@/services/wechat-pay';
import {
  detectWechatAuthParams,
  cleanWechatAuthParams,
  validateWechatAuthParams
} from '@/utils/wechat-pay-utils';
import { urlTool } from '@/utils';
import { to } from '@/utils';

interface WechatAuthProps {
  onAuthSuccess?: (openid: string) => void;
  onAuthError?: (error: Error) => void;
  children?: React.ReactNode;
}

/**
 * 微信公众号授权组件
 * 处理微信网页授权流程，获取用户openid
 */
const WechatAuth: React.FC<WechatAuthProps> = ({
  onAuthSuccess,
  onAuthError,
  children,
}) => {
  const [isAuthorizing, setIsAuthorizing] = useState(false);
  const [openid, setOpenid] = useState<string>('');
  const isMountedRef = useRef(true);

  useEffect(() => {
    // 智能检测授权参数
    const authParams = detectWechatAuthParams();

    console.log('授权参数检测结果:', authParams);

    // 如果URL中直接包含openid（微信公众号消息通知场景）
    if (authParams.hasOpenid && authParams.source === 'url_params') {
      console.log('检测到URL中的openid，直接使用');
      setOpenid(authParams.openid!);

      // 存储授权信息
      // 注意：已移除本地存储逻辑，不再缓存授权数据到localStorage

      onAuthSuccess?.(authParams.openid!);

      // 清理URL参数（可选，保持URL整洁）
      cleanWechatAuthParams(['code', 'state']);

      return;
    }

    // 如果有授权码但没有openid，需要通过授权码获取openid
    if (authParams.hasCode && !authParams.hasOpenid) {
      console.log('检测到授权码，开始获取openid');
      handleAuthCallback(authParams.code!, authParams.state || null);
      return;
    }

    // 注意：已移除本地存储逻辑，不再从localStorage读取授权数据

    // 如果都没有，等待用户主动发起授权
    console.log('未检测到有效授权信息，等待用户授权');

    // 组件卸载时的清理
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  /**
   * 处理授权回调
   * @param code 授权码
   * @param state 状态参数
   */
  const handleAuthCallback = async (code: string, state: string | null) => {
    setIsAuthorizing(true);
    
    try {
      // 验证授权码
      if (!code || code.length < 10) {
        throw new Error('授权码无效，请重新授权');
      }

      // 检查state参数防止CSRF攻击
      const storedState = sessionStorage.getItem('wechat_auth_state');
      if (storedState && state !== storedState) {
        throw new Error('授权状态验证失败，请重新授权');
      }

      const [err, result] = await to(wechatJSAPIPayService.getOpenIdByCode(code));
      
      if (err) {
        throw err;
      }

      if (result?.openid) {
        // 检查组件是否还在挂载状态
        if (!isMountedRef.current) return;
        
        setOpenid(result.openid);
        
        // 注意：已移除本地存储逻辑，不再缓存授权数据到localStorage
        
        // 清理临时状态
        sessionStorage.removeItem('wechat_auth_state');
        
        onAuthSuccess?.(result.openid);
        
        // 清理URL中的授权参数
        try {
          const url = new URL(window.location.href);
          url.searchParams.delete('code');
          url.searchParams.delete('state');
          window.history.replaceState({}, document.title, url.toString());
        } catch (urlError) {
          console.warn('清理URL参数失败:', urlError);
        }
      } else {
        throw new Error('获取用户信息失败，请重新授权');
      }
    } catch (error) {
      console.error('微信授权失败:', error);
      
      // 清理可能的错误状态
      sessionStorage.removeItem('wechat_auth_state');
      
      const errorMessage = error instanceof Error ? error.message : '微信授权失败，请重试';
      Toast.show({
        icon: 'fail',
        content: errorMessage,
        duration: 3000,
      });
      
      onAuthError?.(error as Error);
    } finally {
      setIsAuthorizing(false);
    }
  };

  /**
   * 发起微信授权
   */
  const startWechatAuth = () => {
    if (!wechatJSAPIPayService.isWechatBrowser()) {
      Toast.show({
        icon: 'fail',
        content: '请在微信中打开',
        duration: 3000,
      });
      return;
    }

    try {
      // 检查是否已有有效的openid
      const authData = getValidAuthData();
      if (authData?.openid) {
        setOpenid(authData.openid);
        onAuthSuccess?.(authData.openid);
        return;
      }

      // 构建授权URL
      const redirectUri = window.location.href.split('?')[0]; // 去掉查询参数
      const state = generateSecureState();
      
      // 存储state用于验证
      sessionStorage.setItem('wechat_auth_state', state);
      
      const authUrl = wechatJSAPIPayService.getWechatAuthUrl(redirectUri, state);
      
      // 跳转到微信授权页面
      window.location.href = authUrl;
    } catch (error) {
      console.error('发起微信授权失败:', error);
      Toast.show({
        icon: 'fail',
        content: '发起授权失败，请重试',
        duration: 3000,
      });
    }
  };

  /**
   * 获取有效的授权数据
   * 注意：已移除本地存储逻辑，现在只从URL参数获取授权数据
   */
  const getValidAuthData = () => {
    // 不再从localStorage读取，只从URL参数获取
    const authParams = detectWechatAuthParams();
    if (authParams.hasOpenid) {
      return {
        openid: authParams.openid,
        source: authParams.source,
      };
    }
    return null;
  };

  /**
   * 生成安全的state参数
   */
  const generateSecureState = (): string => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `${timestamp}_${random}`;
  };

  /**
   * 清除授权信息
   */
  const clearAuth = () => {
    try {
      setOpenid('');

      // 安全清理所有相关的存储数据
      const keysToRemove = [
        'wechat_auth_data',
        'wechat_openid', // 兼容旧版本
        'wechat_access_token', // 兼容旧版本
        'wechat_user_info', // 可能的用户信息
      ];

      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`清理localStorage ${key} 失败:`, error);
        }
      });

      // 清理sessionStorage
      try {
        sessionStorage.removeItem('wechat_auth_state');
      } catch (error) {
        console.warn('清理sessionStorage失败:', error);
      }

      // 清理可能的内存引用
      if (typeof window !== 'undefined') {
        delete (window as any).__wechat_auth_cache__;
      }

      Toast.show({
        icon: 'success',
        content: '已清除授权信息',
        duration: 2000,
      });
    } catch (error) {
      console.error('清除授权信息失败:', error);
      Toast.show({
        icon: 'fail',
        content: '清除授权信息失败',
        duration: 2000,
      });
    }
  };

  /**
   * 检查是否已授权
   */
  const isAuthorized = (): boolean => {
    return !!openid || !!getValidAuthData()?.openid;
  };

  /**
   * 获取当前openid
   */
  const getCurrentOpenid = (): string => {
    if (openid) return openid;
    
    const authData = getValidAuthData();
    return authData?.openid || '';
  };

  // 如果传入了children，则渲染children并提供授权方法
  if (children) {
    return (
      <>
        {React.cloneElement(children as React.ReactElement, {
          startWechatAuth,
          clearAuth,
          isAuthorized: isAuthorized(),
          openid: getCurrentOpenid(),
          isAuthorizing,
        })}
      </>
    );
  }

  // 默认渲染：自动检查授权状态
  useEffect(() => {
    const cachedAuthData = getValidAuthData();
    if (cachedAuthData?.openid) {
      setOpenid(cachedAuthData.openid);
      onAuthSuccess?.(cachedAuthData.openid);
    } else if (wechatJSAPIPayService.isWechatBrowser()) {
      // 在微信浏览器中但没有openid，检查是否需要授权
      const code = urlTool.getQueryString('code');
      if (!code) {
        // 没有授权码，需要发起授权
        startWechatAuth();
      }
    }
  }, []);

  if (isAuthorizing) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100px' 
      }}>
        <div>正在授权...</div>
      </div>
    );
  }

  return null;
};

export default observer(WechatAuth);

// 导出Hook形式的使用方式
export const useWechatAuth = () => {
  const [openid, setOpenid] = useState<string>('');
  const [isAuthorizing, setIsAuthorizing] = useState(false);

  const startAuth = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!wechatJSAPIPayService.isWechatBrowser()) {
        reject(new Error('请在微信中打开'));
        return;
      }

      // 智能检测授权参数
      const authParams = detectWechatAuthParams();

      // 如果已有有效的openid，直接返回
      if (authParams.hasOpenid) {
        setOpenid(authParams.openid!);
        resolve(authParams.openid!);
        return;
      }

      // 如果有授权码，通过授权码获取openid
      if (authParams.hasCode) {
        setIsAuthorizing(true);
        wechatJSAPIPayService.getOpenIdByCode(authParams.code!)
          .then((result) => {
            if (result?.openid) {
              setOpenid(result.openid);
              // 注意：已移除本地存储逻辑，不再缓存授权数据到localStorage

              // 清理URL参数
              cleanWechatAuthParams(['code', 'state']);

              resolve(result.openid);
            } else {
              reject(new Error('获取openid失败'));
            }
          })
          .catch(reject)
          .finally(() => setIsAuthorizing(false));
      } else {
        // 发起授权
        const redirectUri = window.location.href.split('?')[0];
        const state = `${Date.now()}_${Math.random().toString(36).substring(2)}`;
        sessionStorage.setItem('wechat_auth_state', state);
        const authUrl = wechatJSAPIPayService.getWechatAuthUrl(redirectUri, state);
        window.location.href = authUrl;
      }
    });
  };

  const clearAuth = () => {
    setOpenid('');
    sessionStorage.removeItem('wechat_auth_state');
  };

  const getCurrentOpenid = (): string => {
    if (openid) return openid;

    // 使用智能检测获取openid
    const authParams = detectWechatAuthParams();
    return authParams.openid || '';
  };

  return {
    openid: getCurrentOpenid(),
    isAuthorizing,
    startAuth,
    clearAuth,
    isAuthorized: !!getCurrentOpenid(),
  };
};
