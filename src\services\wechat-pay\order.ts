import { httpService } from '@/apis';
import { to } from '@/utils';
import { wechatJSAPIPayService } from './index';

/**
 * 微信公众号订单支付服务
 * 专门处理商品订单的微信支付
 */
class WechatOrderPayService {
  /**
   * 创建订单支付并获取支付参数
   * @param orderData 订单支付数据
   * @returns Promise<支付参数>
   */
  async createOrderPayment(orderData: {
    trade_no: string; // 根据接口文档，使用trade_no作为订单标识
    total_fee: string;
    openid: string;
    code?: string; // 微信授权code，传参code和openid二选一
    matching_string?: string; // 机器字符串
    user_id?: string; // 用户ID
    [key: string]: any;
  }) {
    // 参数验证
    if (!orderData.trade_no) {
      throw new Error('缺少交易号(trade_no)');
    }
    
    if (!orderData.total_fee) {
      throw new Error('缺少支付金额');
    }
    
    if (!orderData.openid) {
      throw new Error('缺少用户openid');
    }

    // 验证金额格式
    const amount = parseFloat(orderData.total_fee);
    if (isNaN(amount) || amount <= 0) {
      throw new Error('支付金额无效');
    }

    try {
      // 调用微信支付服务创建订单支付
      const payParams = await wechatJSAPIPayService.createOrderPayment(orderData);
      
      return {
        ...payParams,
        orderInfo: {
          trade_no: orderData.trade_no,
          total_fee: orderData.total_fee,
          description: orderData.description || `订单支付 ¥${orderData.total_fee}`,
        },
      };
    } catch (error) {
      console.error('创建订单支付失败:', error);
      throw error;
    }
  }

  /**
   * 执行订单支付
   * @param orderData 订单支付数据
   * @returns Promise<支付结果>
   */
  async executeOrderPay(orderData: {
    trade_no: string;
    total_fee: string;
    openid: string;
    code?: string;
    matching_string?: string;
    user_id?: string;
    [key: string]: any;
  }) {
    try {
      // 验证支付金额
      const validation = this.validateOrderAmount(orderData.total_fee);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 1. 创建订单支付
      const orderResult = await this.createOrderPayment(orderData);

      // 2. 调起微信支付
      const payResult = await wechatJSAPIPayService.invokeWechatPay(orderResult);

      // 3. 处理支付结果
      if (payResult.resultCode === 'SUCCESS') {
        // 支付成功，查询订单状态确认
        try {
          const orderStatus = await this.queryOrderPaymentStatus(orderData.trade_no);
          return {
            success: true,
            message: '支付成功',
            orderInfo: orderResult.orderInfo,
            orderStatus,
          };
        } catch (queryError) {
          // 即使查询失败，也认为支付成功
          console.warn('查询订单支付状态失败:', queryError);
          return {
            success: true,
            message: '支付成功',
            orderInfo: orderResult.orderInfo,
          };
        }
      } else if (payResult.resultCode === 'CANCEL') {
        return {
          success: false,
          message: '用户取消支付',
          cancelled: true,
        };
      } else {
        throw new Error('支付失败');
      }
    } catch (error) {
      console.error('订单支付失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '支付失败，请重试',
        error,
      };
    }
  }

  /**
   * 查询订单支付状态
   * @param orderId 订单ID
   * @returns Promise<订单状态>
   */
  async queryOrderPaymentStatus(orderId: string) {
    return await wechatJSAPIPayService.queryOrderPaymentStatus(orderId);
  }

  /**
   * 获取订单支付历史记录
   * @param params 查询参数
   * @returns Promise<支付历史>
   */
  async getOrderPaymentHistory(params: {
    page?: number;
    limit?: number;
    start_date?: string;
    end_date?: string;
    status?: string;
  } = {}) {
    const [err, res] = await to(
      httpService<{
        list: Array<{
          order_id: string;
          amount: string;
          status: string;
          created_at: string;
          pay_time?: string;
          trade_no?: string;
          product_name?: string;
        }>;
        total: number;
        page: number;
        limit: number;
      }>({
        url: '/order/payment/history',
        method: 'POST',
        data: {
          page: params.page || 1,
          limit: params.limit || 20,
          start_date: params.start_date,
          end_date: params.end_date,
          status: params.status,
        },
      })
    );

    if (err) {
      throw new Error('获取订单支付历史失败');
    }

    return res;
  }

  /**
   * 验证订单支付金额
   * @param amount 金额
   * @returns 验证结果
   */
  validateOrderAmount(amount: string | number): {
    isValid: boolean;
    error?: string;
  } {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount)) {
      return { isValid: false, error: '金额格式错误' };
    }
    
    if (numAmount <= 0) {
      return { isValid: false, error: '支付金额必须大于0' };
    }
    
    if (numAmount < 0.01) {
      return { isValid: false, error: '最小支付金额为0.01元' };
    }
    
    if (numAmount > 50000) {
      return { isValid: false, error: '单次支付金额不能超过50000元' };
    }
    
    // 检查小数位数
    const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
      return { isValid: false, error: '金额最多支持2位小数' };
    }
    
    return { isValid: true };
  }

  /**
   * 格式化订单支付金额
   * @param amount 金额
   * @returns 格式化后的金额字符串
   */
  formatOrderAmount(amount: string | number): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount)) {
      return '0.00';
    }
    
    return numAmount.toFixed(2);
  }

  /**
   * 获取订单支付状态描述
   * @param status 状态码
   * @returns 状态描述
   */
  getOrderPaymentStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'SUCCESS': '支付成功',
      'REFUND': '已退款',
      'NOTPAY': '未支付',
      'CLOSED': '已关闭',
      'REVOKED': '已撤销',
      'USERPAYING': '用户支付中',
      'PAYERROR': '支付失败',
      'PENDING': '待处理',
    };
    
    return statusMap[status] || '未知状态';
  }

  /**
   * 检查订单是否可以支付
   * @param orderInfo 订单信息
   * @returns 检查结果
   */
  canPayOrder(orderInfo: {
    status?: string;
    amount?: string | number;
    expired_at?: string;
  }): {
    canPay: boolean;
    reason?: string;
  } {
    // 检查订单状态
    if (orderInfo.status === 'SUCCESS') {
      return { canPay: false, reason: '订单已支付' };
    }
    
    if (orderInfo.status === 'CLOSED') {
      return { canPay: false, reason: '订单已关闭' };
    }
    
    if (orderInfo.status === 'CANCELLED') {
      return { canPay: false, reason: '订单已取消' };
    }
    
    // 检查订单金额
    if (orderInfo.amount) {
      const validation = this.validateOrderAmount(orderInfo.amount);
      if (!validation.isValid) {
        return { canPay: false, reason: validation.error };
      }
    }
    
    // 检查订单是否过期
    if (orderInfo.expired_at) {
      const expiredTime = new Date(orderInfo.expired_at).getTime();
      if (Date.now() > expiredTime) {
        return { canPay: false, reason: '订单已过期' };
      }
    }
    
    return { canPay: true };
  }
}

// 导出单例
export const wechatOrderPayService = new WechatOrderPayService();
