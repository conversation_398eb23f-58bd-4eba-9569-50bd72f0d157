import { useCallback, useState } from 'react';
import { Modal } from 'antd-mobile';
import useVipText from '@/hooks/useVipText';
import RootStore from '@/stores';

const useVipModals = () => {
  const { isAccSupervise, isVipMember } = RootStore?.instance?.userStore?.getVipStatus;
  const vipText = useVipText();
  console.log('@@@@@vipText', vipText);
  const showVipModal = (callback) => {
    console.log('@@@vipText22', vipText);
    Modal.alert({
      title: `监管${isAccSupervise ? '账号' : '成员'}达到上限`,
      content: vipText,
      showCloseButton: true,
      onConfirm: () => {
        callback();
      },
      onClose: () => {
        callback();
      },
      confirmText: isAccSupervise
        ? isVipMember
          ? '加购'
          : '立即升级'
        : isVipMember
        ? '加购'
        : '开通会员',
    });
  };
  return { showVipModal };
};

export default useVipModals;
